@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 97%;
    --foreground: 220 4% 27%;

    --card: 0 0% 100%;
    --card-foreground: 220 4% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 4% 27%;

    --primary: 4 56% 52%;
    --primary-foreground: 0 0% 100%;

    --secondary: 200 5% 30%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 5% 93%;
    --muted-foreground: 220 3% 50%;

    --accent: 210 5% 93%;
    --accent-foreground: 220 4% 27%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 3% 90%;
    --input: 220 3% 90%;
    --ring: 4 56% 52%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 4 56% 52%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 4 56% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
}

@layer components {
  .container-taiyo {
    @apply mx-auto w-full max-w-[1280px] px-4;
  }

  .btn-primary {
    @apply bg-primary text-white py-2 px-4 rounded hover:bg-primary/90 transition-colors;
  }

  .header-link {
    @apply text-sm hover:text-primary transition-colors;
  }

  .footer-link {
    @apply text-sm hover:text-white transition-colors;
  }

  /* Taiyo-specific styles */
  .taiyo-section-title {
    @apply text-2xl sm:text-3xl font-bold text-center mb-8;
  }

  .taiyo-card {
    @apply bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow;
  }

  .taiyo-button {
    @apply bg-taiyo-red text-white px-8 py-4 rounded-sm hover:bg-taiyo-red/90 transition-colors font-medium;
  }

  .taiyo-button-outline {
    @apply border-2 border-taiyo-red text-taiyo-red px-8 py-4 rounded-sm hover:bg-taiyo-red hover:text-white transition-colors font-medium;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
