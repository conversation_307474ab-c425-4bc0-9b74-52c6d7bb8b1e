import Image from "next/image"
import Link from "next/link"

export function TechnicalResourcesSidebar() {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-800 text-white p-4">
        <h3 className="font-bold text-lg">技術資料一覧</h3>
      </div>
      
      {/* Technical Resources */}
      <div className="p-4 space-y-4">
        {/* Resource 1 */}
        <Link href="/diecast-navi/technical-data#screw-fastening" className="block hover:opacity-80 transition-opacity">
          <Image
            src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2021/08/aef053c80c71c1a529ee40e64d73fa87.jpg"
            alt="ダイカスト 鋳造欠陥対策ガイドブック"
            width={250}
            height={180}
            className="w-full h-auto rounded"
          />
          <p className="text-xs text-gray-600 mt-2 leading-tight">
            ダイカスト 鋳造欠陥対策ガイドブック<br />
            コストダウン・高品質化 ガイドブック
          </p>
        </Link>

        {/* Resource 2 */}
        <Link href="/diecast-navi/technical-data#die-casting" className="block hover:opacity-80 transition-opacity">
          <Image
            src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2021/08/795316b92fc766b0181f6fef074f03fa.jpg"
            alt="ダイカスト技術ハンドブック"
            width={250}
            height={180}
            className="w-full h-auto rounded"
          />
          <p className="text-xs text-gray-600 mt-2 leading-tight">
            ダイカスト VA・VE<br />
            技術ハンドブック
          </p>
        </Link>

        {/* Resource 3 */}
        <Link href="/diecast-navi/technical-data#sand-casting" className="block hover:opacity-80 transition-opacity">
          <Image
            src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2021/07/8b0004f12163627de76c10f23ee24f41.jpg"
            alt="特許製法ガイドブック"
            width={250}
            height={180}
            className="w-full h-auto rounded"
          />
          <p className="text-xs text-gray-600 mt-2 leading-tight">
            特許製法<br />
            ガイドブック
          </p>
        </Link>
      </div>

      {/* Popular Articles Section */}
      <div className="border-t border-gray-200">
        <div className="bg-gray-100 p-4">
          <h4 className="font-bold text-gray-800">技術情報・技術コラム 人気記事</h4>
        </div>
        <div className="p-4 space-y-3">
          <Link href="/diecast-navi/column/643.html" className="flex items-start gap-3 hover:bg-gray-50 p-2 rounded transition-colors">
            <div className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold flex-shrink-0">NO.1</div>
            <div>
              <h5 className="text-sm font-medium text-gray-800 hover:text-red-500 transition-colors leading-tight">
                チル層の役割と破断チル層の対策とは
              </h5>
            </div>
          </Link>

          <Link href="/diecast-navi/column/482.html" className="flex items-start gap-3 hover:bg-gray-50 p-2 rounded transition-colors">
            <div className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold flex-shrink-0">NO.2</div>
            <div className="flex items-start gap-2">
              <Image
                src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2021/08/be804e357e109be49015e22dcaca47b4-1-150x150.jpg"
                alt="ダイカストにおける巣の原因とその対策"
                width={40}
                height={40}
                className="rounded flex-shrink-0"
              />
              <h5 className="text-sm font-medium text-gray-800 hover:text-red-500 transition-colors leading-tight">
                ダイカストにおける巣の原因とその対策
              </h5>
            </div>
          </Link>

          <Link href="/diecast-navi/column/684.html" className="flex items-start gap-3 hover:bg-gray-50 p-2 rounded transition-colors">
            <div className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold flex-shrink-0">NO.3</div>
            <div className="flex items-start gap-2">
              <Image
                src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2022/01/edbdb730c1919b8d5456315ee228415b-150x150.jpg"
                alt="亜鉛ダイカストの特徴と素材について"
                width={40}
                height={40}
                className="rounded flex-shrink-0"
              />
              <h5 className="text-sm font-medium text-gray-800 hover:text-red-500 transition-colors leading-tight">
                亜鉛ダイカストの特徴と素材について
              </h5>
            </div>
          </Link>

          <Link href="/diecast-navi/column/670.html" className="flex items-start gap-3 hover:bg-gray-50 p-2 rounded transition-colors">
            <div className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold flex-shrink-0">NO.4</div>
            <div className="flex items-start gap-2">
              <Image
                src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2021/07/kouhouhenkan26-2-150x150.png"
                alt="アルミダイカストにおける材料の種類"
                width={40}
                height={40}
                className="rounded flex-shrink-0"
              />
              <h5 className="text-sm font-medium text-gray-800 hover:text-red-500 transition-colors leading-tight">
                アルミダイカストにおける材料の種類
              </h5>
            </div>
          </Link>

          <Link href="/diecast-navi/column/728.html" className="flex items-start gap-3 hover:bg-gray-50 p-2 rounded transition-colors">
            <div className="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold flex-shrink-0">NO.5</div>
            <div className="flex items-start gap-2">
              <Image
                src="https://www.taiyoparts.co.jp/diecast-navi/wp-content/uploads/2022/05/b44d784bf1e5f95b64995d581a29eb38-150x150.jpg"
                alt="マグネシウムダイカストの特徴と素材について"
                width={40}
                height={40}
                className="rounded flex-shrink-0"
              />
              <h5 className="text-sm font-medium text-gray-800 hover:text-red-500 transition-colors leading-tight">
                マグネシウムダイカストの特徴と素材について
              </h5>
            </div>
          </Link>
        </div>
      </div>

      {/* Cost Down Service Links */}
      <div className="border-t border-gray-200">
        <div className="bg-gray-100 p-4">
          <h4 className="font-bold text-gray-800">コストダウン提案サービス</h4>
        </div>
        <div className="p-4 space-y-2">
          <Link href="/diecast-navi/service" className="block text-sm p-2 hover:bg-gray-50 rounded transition-colors">
            <span className="font-bold text-red-500">ロストワックス鋳造品</span>をご使用中の皆様へ
          </Link>
          <Link href="/diecast-navi/service/sand-casting" className="block text-sm p-2 hover:bg-gray-50 rounded transition-colors">
            <span className="font-bold text-red-500">砂型鋳造品</span>をご使用中の皆様へ
          </Link>
          <Link href="/diecast-navi/service/cut-product" className="block text-sm p-2 hover:bg-gray-50 rounded transition-colors">
            <span className="font-bold text-red-500">切削品</span>をご使用中の皆様へ
          </Link>
          <Link href="/diecast-navi/service/screw-fastening" className="block text-sm p-2 hover:bg-gray-50 rounded transition-colors">
            <span className="font-bold text-red-500">複合品(ビス締結)</span>をご使用中の皆様へ
          </Link>
          <Link href="/diecast-navi/service/welding" className="block text-sm p-2 hover:bg-gray-50 rounded transition-colors">
            <span className="font-bold text-red-500">複合品(溶接)</span>をご使用中の皆様へ
          </Link>
          <Link href="/diecast-navi/service/die-casting" className="block text-sm p-2 hover:bg-gray-50 rounded transition-colors">
            <span className="font-bold text-red-500">従来のダイカスト</span>をご使用中の皆様へ
          </Link>
        </div>
      </div>
    </div>
  )
}
