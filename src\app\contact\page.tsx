import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/contact-top.jpg"
          alt="お問い合わせ"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-taiyo">
            <h1 className="text-3xl md:text-4xl font-bold text-white">お問い合わせ</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-taiyo">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">お問い合わせ</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-taiyo max-w-3xl">
          <p className="text-center text-gray-700 mb-8">
            華日パーツへのお問い合わせは、下記の電話番号またはお問い合わせフォームより受け付けております。<br />
            お気軽にお問い合わせください。
          </p>

          <div className="flex flex-col md:flex-row items-center justify-center gap-8 mb-12">
            <Card className="w-full md:w-1/2 border-none shadow-lg">
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-4 text-center">お電話でのお問い合わせ</h2>
                <div className="text-center">
                  <a href="tel:0723612111" className="text-2xl md:text-3xl font-bold text-taiyo-red flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                    ************
                  </a>
                  <p className="text-sm text-gray-500 mt-2">
                    営業時間：9:00〜17:00（土日祝休み）
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="w-full md:w-1/2 border-none shadow-lg">
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-4 text-center">FAX でのお問い合わせ</h2>
                <div className="text-center">
                  <p className="text-2xl md:text-3xl font-bold text-taiyo-dark flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17 17h.01"></path><path d="M7 11h10v6a3 3 0 0 1-3 3H10a3 3 0 0 1-3-3v-6z"></path><path d="M7 11v-4a4 4 0 0 1 4-4h2a4 4 0 0 1 4 4v4"></path><path d="M11 15h2"></path></svg>
                    072-362-3333
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    24時間受付
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div className="bg-[#f7f7f7] p-6 md:p-8 rounded-md shadow-sm">
            <h2 className="text-xl font-bold mb-6 text-center">お問い合わせフォーム</h2>

            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:text-right pt-2">
                  <label className="font-medium">
                    <span className="text-taiyo-red mr-1">*</span>
                    お名前
                  </label>
                </div>
                <div className="md:col-span-3">
                  <input
                    type="text"
                    className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">例：山田 太郎</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:text-right pt-2">
                  <label className="font-medium">
                    <span className="text-taiyo-red mr-1">*</span>
                    メールアドレス
                  </label>
                </div>
                <div className="md:col-span-3">
                  <input
                    type="email"
                    className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">例：<EMAIL></p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:text-right pt-2">
                  <label className="font-medium">会社名</label>
                </div>
                <div className="md:col-span-3">
                  <input
                    type="text"
                    className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:text-right pt-2">
                  <label className="font-medium">電話番号</label>
                </div>
                <div className="md:col-span-3">
                  <input
                    type="tel"
                    className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                  <p className="text-xs text-gray-500 mt-1">例：************</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:text-right pt-2">
                  <label className="font-medium">
                    <span className="text-taiyo-red mr-1">*</span>
                    お問い合わせ内容
                  </label>
                </div>
                <div className="md:col-span-3">
                  <textarea
                    rows={5}
                    className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
                    required
                  ></textarea>
                </div>
              </div>

              <div className="text-center mt-8">
                <Button type="submit" className="bg-taiyo-red hover:bg-taiyo-red/90 text-white px-8 py-3 rounded-sm">
                  送信する
                </Button>
              </div>
            </form>
          </div>

          <div className="mt-12 p-6 border-t border-gray-200">
            <h3 className="font-bold mb-4">個人情報の取り扱いについて</h3>
            <p className="text-sm text-gray-700 mb-4">
              当社は、お客様からお預かりした個人情報を大切に保護することを企業の重要な社会的責任と位置づけ、個人情報の保護に努めることをお約束いたします。
            </p>
            <p className="text-sm text-gray-700">
              個人情報の取り扱いにつきましては、「個人情報保護方針」をご参照ください。
              お問い合わせフォームからのお問い合わせは、内容によって回答までにお時間をいただく場合がございます。
              また、営業時間外にいただいたお問い合わせに関しましては、翌営業日以降の対応となりますので、あらかじめご了承ください。
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
