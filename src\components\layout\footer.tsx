"use client"

import Link from "next/link"
import Image from "next/image"

export function Footer() {
  return (
    <footer className="w-full">
      {/* Banner section */}
      <div className="bg-[#333] pt-3 pb-6">
        <div className="container-taiyo flex flex-wrap justify-center gap-4">
          <Link href="/diecast-navi" className="hover:opacity-80 transition-opacity">
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/banner5.png"
              alt="ダイカスト鋳造コストダウンNavi"
              width={200}
              height={60}
              className="h-auto"
            />
          </Link>
          <Link href="/blog/5162/" className="hover:opacity-80 transition-opacity">
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/banner1.png"
              alt="足踏み式消毒液スタンド フムトデール"
              width={200}
              height={60}
              className="h-auto"
            />
          </Link>
          <Link href="/tafwan/top.html" className="hover:opacity-80 transition-opacity">
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/banner2.png"
              alt="腕上げ作業アシストスーツ TAFWAN"
              width={200}
              height={60}
              className="h-auto"
            />
          </Link>
          <Link href="/services/development/#c3" className="hover:opacity-80 transition-opacity">
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/banner3.png"
              alt="華日パーツのオリジナル商品"
              width={200}
              height={60}
              className="h-auto"
            />
          </Link>
          <Link href="/recruit" className="hover:opacity-80 transition-opacity">
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/banner4.png"
              alt="採用情報"
              width={200}
              height={60}
              className="h-auto"
            />
          </Link>
        </div>
      </div>

      {/* Main footer with links */}
      <div className="bg-[#464f52] py-8 text-white">
        <div className="container-taiyo">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Company info and logo */}
            <div className="lg:col-span-2">
              <Link href="/" className="block mb-4">
                <Image
                  src="/images/logos/hr-logo.png"
                  alt="寧波市北倫華日金属製品有限公司"
                  width={120}
                  height={30}
                  className="h-auto"
                />
              </Link>
              <p className="text-sm text-gray-300 mb-1">〒587-0065</p>
              <p className="text-sm text-gray-300 mb-1">大阪府堺市美原区小寺402-1</p>
              <p className="text-sm text-gray-300 mb-4">TEL：************</p>

              {/* Social media icons */}
              <div className="flex gap-3">
                <a href="https://www.youtube.com/channel/UCnJn3EATohaglDSMueWvKBw" target="_blank" rel="noopener noreferrer" className="hover:opacity-80 transition-opacity">
                  <Image
                    src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/youtube.png"
                    alt="華日パーツ YouTubeチャンネル"
                    width={24}
                    height={24}
                  />
                </a>
                <a href="https://twitter.com/UsrEuORzK7ORDqz" target="_blank" rel="noopener noreferrer" className="hover:opacity-80 transition-opacity">
                  <Image
                    src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/twitter.png"
                    alt="華日パーツ公式Twitter"
                    width={24}
                    height={24}
                  />
                </a>
                <a href="https://www.facebook.com/%E5%A4%AA%E9%99%BD%E3%83%91%E3%83%BC%E3%83%84-111947610544398" target="_blank" rel="noopener noreferrer" className="hover:opacity-80 transition-opacity">
                  <Image
                    src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/facebook.png"
                    alt="華日パーツ公式Facebook"
                    width={24}
                    height={24}
                  />
                </a>
                <a href="https://www.instagram.com/taiyo_parts/" target="_blank" rel="noopener noreferrer" className="hover:opacity-80 transition-opacity">
                  <Image
                    src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/instagram.png"
                    alt="華日パーツ公式Instagram"
                    width={24}
                    height={24}
                  />
                </a>
              </div>
            </div>

            {/* Navigation columns */}
            <div className="lg:col-span-1">
              <ul className="space-y-3">
                <li>
                  <Link href="/advantage" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    華日パーツの強み
                  </Link>
                </li>

                <li>
                  <Link href="/manufacturer_function" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    製造力
                  </Link>
                </li>
                <li>
                  <Link href="/product" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    製作事例
                  </Link>
                </li>
                <li>
                  <Link href="/scene" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    こんなところに華日パーツ！
                  </Link>
                </li>
                <li>
                  <Link href="/equipment" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    既存の設備
                  </Link>
                </li>
                <li>
                  <Link href="/technology" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    製造技術
                  </Link>
                </li>
              </ul>
            </div>

            <div className="lg:col-span-1">
              <ul className="space-y-3">
                <li>
                  <Link href="/company" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    企業情報
                  </Link>
                  <ul className="ml-4 mt-2 space-y-1">
                    <li>
                      <Link href="/company" className="text-xs text-gray-400 hover:text-white transition-colors">
                        ご挨拶
                      </Link>
                    </li>
                    <li>
                      <Link href="/company/about" className="text-xs text-gray-400 hover:text-white transition-colors">
                        会社概要・事業所・沿革
                      </Link>
                    </li>
                    <li>
                      <Link href="/company/csr" className="text-xs text-gray-400 hover:text-white transition-colors">
                        社会貢献活動
                      </Link>
                    </li>
                  </ul>
                </li>
                <li>
                  <Link href="/recruit" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    採用情報
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    お問い合わせ
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    よくあるご質問
                  </Link>
                </li>
                <li>
                  <Link href="/news" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    お知らせ
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    ブログ
                  </Link>
                </li>
              </ul>
            </div>

            <div className="lg:col-span-1">
              <ul className="space-y-3">
                <li>
                  <Link href="/privacy" className="inline-block text-sm text-gray-300 hover:text-white transition-colors border border-gray-500 rounded-full px-4 py-2">
                    プライバシーポリシー
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="bg-[#464f52] py-4 border-t border-gray-600">
        <div className="container-taiyo text-center">
          <p className="text-xs text-gray-400">
            Copyright © HUARI CO., Ltd.
          </p>
        </div>
      </div>
    </footer>
  )
}
